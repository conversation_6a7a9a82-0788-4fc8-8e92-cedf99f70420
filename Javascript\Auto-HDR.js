// ==UserScript==
// @name         Auto HDR Optimized
// @namespace    http://taeparlaytampermonkey.net/
// @version      3.1
// @description  Optimized HDR effect with minimal overhead
// <AUTHOR>
// @match        *://*/*
// @grant        GM_getValue
// @grant        GM_setValue
// @grant        GM_addStyle
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // Constants
    const SCRIPT_NAME = 'AutoHDRSettings';
    const processedElements = new WeakSet();
    let cssFilterString = '';
    let mutationObserver = null;

    // Default settings
    const DEFAULT_SETTINGS = {
        enabled: true,
        brightness: 1.05,
        contrast: 1.15,
        saturation: 1.20,
        excludedSites: [],
        maxCanvasDimension: 1500,
        enableGUI: true
    };

    let settings = { ...DEFAULT_SETTINGS };

    // Settings management
    function loadSettings() {
        try {
            const saved = GM_getValue(SCRIPT_NAME, null);
            if (saved) {
                settings = { ...DEFAULT_SETTINGS, ...JSON.parse(saved) };
                validateSettings();
            }
        } catch (e) {
            settings = { ...DEFAULT_SETTINGS };
        }
    }

    function validateSettings() {
        ['brightness', 'contrast', 'saturation'].forEach(field => {
            const val = parseFloat(settings[field]);
            settings[field] = isNaN(val) ? DEFAULT_SETTINGS[field] : Math.max(0.1, Math.min(3, val));
        });

        settings.maxCanvasDimension = Math.max(500, Math.min(3000, parseInt(settings.maxCanvasDimension) || DEFAULT_SETTINGS.maxCanvasDimension));
        settings.enabled = Boolean(settings.enabled);
        settings.enableGUI = Boolean(settings.enableGUI);
        settings.excludedSites = Array.isArray(settings.excludedSites) ? settings.excludedSites : [];
    }

    const saveSettings = () => GM_setValue(SCRIPT_NAME, JSON.stringify(settings));

    // Optimized helper functions
    const isCrossOrigin = (img) => {
        try {
            return !img.src.startsWith('data:') &&
                   new URL(img.src, window.location.href).origin !== window.location.origin;
        } catch {
            return true;
        }
    };

    const isSiteExcluded = () =>
        settings.excludedSites.some(site => site && window.location.href.includes(site.trim()));

    // Optimized HDR processing
    function applyHDREffectToImage(img) {
        if (processedElements.has(img) || !img.complete || !img.naturalWidth) return;

        // Generate filter string if needed
        if (!cssFilterString) {
            cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
        }

        // Use CSS filter for large images or cross-origin
        if (img.naturalWidth > settings.maxCanvasDimension ||
            img.naturalHeight > settings.maxCanvasDimension ||
            isCrossOrigin(img)) {
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = 'css';
        } else {
            processImageWithCanvas(img);
        }

        processedElements.add(img);
    }

    function processImageWithCanvas(img) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = img.naturalWidth;
        canvas.height = img.naturalHeight;

        try {
            ctx.drawImage(img, 0, 0);
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            const { brightness, contrast, saturation } = settings;

            // Optimized HDR processing
            for (let i = 0; i < data.length; i += 4) {
                let r = data[i], g = data[i + 1], b = data[i + 2];

                // Combined brightness and contrast
                r = ((r - 128) * contrast + 128) * brightness;
                g = ((g - 128) * contrast + 128) * brightness;
                b = ((b - 128) * contrast + 128) * brightness;

                // Saturation adjustment
                const gray = 0.299 * r + 0.587 * g + 0.114 * b;
                r = gray + (r - gray) * saturation;
                g = gray + (g - gray) * saturation;
                b = gray + (b - gray) * saturation;

                // Clamp and assign
                data[i] = Math.max(0, Math.min(255, r));
                data[i + 1] = Math.max(0, Math.min(255, g));
                data[i + 2] = Math.max(0, Math.min(255, b));
            }

            ctx.putImageData(imageData, 0, 0);

            if (!img.src.startsWith('data:')) {
                img.dataset.originalSrc = img.src;
            }
            img.src = canvas.toDataURL();
            img.dataset.hdrApplied = 'canvas';

        } catch (e) {
            // Fallback to CSS filter
            img.style.filter = cssFilterString;
            img.dataset.hdrApplied = 'css-fallback';
        }
    }

    const applyHDRToVideos = () => {
        if (!cssFilterString) {
            cssFilterString = `brightness(${settings.brightness}) contrast(${settings.contrast}) saturate(${settings.saturation})`;
        }

        document.querySelectorAll('video:not([data-hdrApplied])').forEach(video => {
            video.style.filter = cssFilterString;
            video.dataset.hdrApplied = 'video';
            processedElements.add(video);
        });
    };

    const revertElement = (el) => {
        if (!el.dataset.hdrApplied) return;

        el.style.filter = '';

        if (el.dataset.hdrApplied.includes('canvas') && el.dataset.originalSrc) {
            el.src = el.dataset.originalSrc;
        }

        ['data-hdrApplied', 'data-originalSrc'].forEach(attr => el.removeAttribute(attr));
        processedElements.delete(el);
    };

    function processAllMedia() {
        if (!settings.enabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            return;
        }

        // Reset filter string to ensure fresh values
        cssFilterString = '';

        // Process images
        document.querySelectorAll('img:not([data-hdrApplied])').forEach(img => {
            if (img.complete) {
                applyHDREffectToImage(img);
            } else {
                const onLoad = () => applyHDREffectToImage(img);
                img.addEventListener('load', onLoad, { once: true });
            }
        });

        applyHDRToVideos();
    }

    const debounce = (fn, delay) => {
        let timeoutId;
        return (...args) => {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn(...args), delay);
        };
    };

    const debouncedProcessMedia = debounce(processAllMedia, 100);

    function startObserver() {
        mutationObserver?.disconnect();

        if (!settings.enabled || isSiteExcluded()) {
            document.querySelectorAll('[data-hdrApplied]').forEach(revertElement);
            return;
        }

        mutationObserver = new MutationObserver((mutations) => {
            const hasNewMedia = mutations.some(mutation =>
                mutation.type === 'childList' &&
                Array.from(mutation.addedNodes).some(node =>
                    node.nodeType === 1 && (
                        ['IMG', 'VIDEO'].includes(node.tagName) ||
                        node.querySelector?.('img, video')
                    )
                )
            );

            if (hasNewMedia) debouncedProcessMedia();
        });

        const target = document.body || document.documentElement;
        if (target) {
            mutationObserver.observe(target, { childList: true, subtree: true });
        }

        debouncedProcessMedia();
    }


    function init() {
        loadSettings();

        if (settings.enableGUI) {
            const initGUI = () => createSettingsGUI();
            document.body ? initGUI() : document.addEventListener('DOMContentLoaded', initGUI, { once: true });
        }

        startObserver();

        // Listen for settings changes
        window.addEventListener('storage', (event) => {
            if (event.key === SCRIPT_NAME) {
                const oldEnabled = settings.enabled;
                loadSettings();
                if (oldEnabled !== settings.enabled) startObserver();
            }
        });

        // Initial processing
        const processInitial = () => debouncedProcessMedia();
        document.readyState === 'complete' ? processInitial() :
            window.addEventListener('load', processInitial, { once: true });
    }

    // Simple GUI
    function createSettingsGUI() {
        if (document.getElementById('autohdr-settings-button') || !document.body) return;

        GM_addStyle(`
            #autohdr-hover-area {
                position: fixed; top: 0; right: 0; width: 80px; height: 80px; z-index: 9998;
                background: transparent; pointer-events: auto;
            }
            #autohdr-settings-button {
                position: fixed; top: 20px; right: -60px; z-index: 9999;
                background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; border: none;
                padding: 12px 16px; border-radius: 8px 0 0 8px; cursor: pointer; font-weight: bold;
                box-shadow: 0 4px 12px rgba(59,130,246,0.3); transition: all 0.3s ease;
                opacity: 0.8; transform: translateX(0);
            }
            #autohdr-hover-area:hover #autohdr-settings-button {
                right: 0; opacity: 1; transform: translateX(0) translateY(-2px);
                box-shadow: 0 6px 16px rgba(59,130,246,0.4);
            }
            #autohdr-settings-panel {
                position: fixed; top: 70px; right: -340px; z-index: 9997;
                background: rgba(15,15,15,0.95); color: white; padding: 24px;
                border-radius: 12px 0 0 12px; width: 320px; backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px rgba(0,0,0,0.6); border: 1px solid rgba(255,255,255,0.1);
                transition: all 0.3s ease; opacity: 0;
            }
            #autohdr-settings-panel.show {
                right: 0; opacity: 1; animation: slideIn 0.3s ease;
            }
            #autohdr-settings-panel h3 { margin: 0 0 16px 0; color: #3b82f6; font-size: 18px; }
            #autohdr-settings-panel label { display: block; margin: 12px 0; font-size: 14px; }
            #autohdr-settings-panel input[type="number"] {
                width: 80px; margin-left: 10px; padding: 6px 8px; border-radius: 6px;
                border: 1px solid #444; background: #2a2a2a; color: white;
            }
            #autohdr-settings-panel input[type="checkbox"] { margin-right: 8px; transform: scale(1.2); }
            #autohdr-settings-panel textarea {
                width: 100%; height: 60px; margin-top: 8px; padding: 8px; border-radius: 6px;
                border: 1px solid #444; background: #2a2a2a; color: white; resize: vertical;
            }
            #autohdr-settings-panel button {
                background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; border: none;
                padding: 12px 24px; border-radius: 8px; cursor: pointer; margin-top: 16px;
                width: 100%; font-weight: bold; transition: all 0.2s ease;
            }
            #autohdr-settings-panel button:hover { background: linear-gradient(135deg, #2563eb, #1e40af); }
            @keyframes slideIn {
                from { opacity: 0; right: -340px; transform: translateX(-20px); }
                to { opacity: 1; right: 0; transform: translateX(0); }
            }
        `);

        // Create hover area
        const hoverArea = document.createElement('div');
        hoverArea.id = 'autohdr-hover-area';

        const button = document.createElement('button');
        button.id = 'autohdr-settings-button';
        button.textContent = 'HDR';

        const panel = document.createElement('div');
        panel.id = 'autohdr-settings-panel';
        panel.innerHTML = `
            <h3>HDR Settings</h3>
            <label><input type="checkbox" id="enabled"> Enable HDR</label>
            <label>Brightness: <input type="number" id="brightness" step="0.01" min="0.1" max="3" value="${settings.brightness}"></label>
            <label>Contrast: <input type="number" id="contrast" step="0.01" min="0.1" max="3" value="${settings.contrast}"></label>
            <label>Saturation: <input type="number" id="saturation" step="0.01" min="0.1" max="3" value="${settings.saturation}"></label>
            <label>Excluded Sites:<br><textarea id="excludedSites" placeholder="site1.com, site2.com">${settings.excludedSites.join(', ')}</textarea></label>
            <button id="save">Save & Apply</button>
        `;

        // Enhanced hover and click functionality
        let hoverTimeout;

        hoverArea.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
        });

        hoverArea.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                if (!panel.matches(':hover')) {
                    panel.classList.remove('show');
                }
            }, 300);
        });

        button.addEventListener('click', () => {
            const isVisible = panel.classList.toggle('show');
            if (isVisible) {
                document.getElementById('enabled').checked = settings.enabled;
            }
        });

        // Keep panel open when hovering over it
        panel.addEventListener('mouseenter', () => {
            clearTimeout(hoverTimeout);
        });

        panel.addEventListener('mouseleave', () => {
            hoverTimeout = setTimeout(() => {
                panel.classList.remove('show');
            }, 300);
        });

        panel.addEventListener('click', (e) => {
            if (e.target.id === 'save') {
                settings.enabled = document.getElementById('enabled').checked;
                settings.brightness = Math.max(0.1, Math.min(3, parseFloat(document.getElementById('brightness').value) || 1));
                settings.contrast = Math.max(0.1, Math.min(3, parseFloat(document.getElementById('contrast').value) || 1));
                settings.saturation = Math.max(0.1, Math.min(3, parseFloat(document.getElementById('saturation').value) || 1));
                settings.excludedSites = document.getElementById('excludedSites').value
                    .split(',').map(s => s.trim()).filter(s => s);

                cssFilterString = '';
                saveSettings();
                startObserver();
                panel.classList.remove('show');
            }
        });

        // Append hover area first, then button inside it, then panel
        hoverArea.appendChild(button);
        document.body.append(hoverArea, panel);
    }

    // Initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        init();
    }

})();
